#!/usr/bin/env python3
"""
Intelligent Automotive Software Testing Assistant
Integrates with GitLab CI/CD and uses Llama 3 8B via Ollama for automated testing insights
"""

import requests
import re
import json
import subprocess
import sys
from datetime import datetime
from typing import List, Dict, Optional, Any
import argparse
from dataclasses import dataclass

# === Configuration ===
GITLAB_TOKEN = "**************************"
PROJECT_ID = "71217006"
BRANCH = "main"
BASE_URL = "https://gitlab.com/api/v4"
HEADERS = {"PRIVATE-TOKEN": GITLAB_TOKEN}

# Ollama configuration
OLLAMA_URL = "http://localhost:11434"
MODEL_NAME = "llama3:8b-instruct-q4_0"

@dataclass
class TestAnalysis:
    """Data structure for test analysis results"""
    file_path: str
    test_cases: List[str]
    commit_history: List[Dict]
    pipeline_status: Dict
    analysis: str
    recommendations: List[str]

class GitLabTestingAgent:
    """Intelligent agent for automotive software testing with GitLab integration"""
    
    def __init__(self):
        self.conversation_history = []
        self.current_context = {}
        
    def get_repo_tree(self) -> List[Dict]:
        """Fetch all Python test files from repository"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/tree"
            params = {"ref": BRANCH, "recursive": True}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            return [f for f in r.json() if f["path"].endswith(".py")]
        except Exception as e:
            print(f"❌ Error fetching repository tree: {e}")
            return []

    def get_test_cases(self, file_path: str) -> List[str]:
        """Extract test cases from a Python file"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/files/{requests.utils.quote(file_path, safe='')}/raw"
            params = {"ref": BRANCH}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            content = r.text
            
            # Enhanced regex to capture more test patterns
            test_patterns = [
                r"def (test_\w+)\(",
                r"def (Test\w+)\(",
                r"class (Test\w+)\(",
                r"@pytest\.mark\.\w+\s*\ndef (\w+)\("
            ]
            
            test_cases = []
            for pattern in test_patterns:
                test_cases.extend(re.findall(pattern, content))
            
            return list(set(test_cases))  # Remove duplicates
        except Exception as e:
            print(f"❌ Error extracting test cases: {e}")
            return []

    def get_commits_for_file(self, file_path: str) -> List[Dict]:
        """Get commit history for a specific file"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/commits"
            params = {"path": file_path, "ref_name": BRANCH}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            return r.json()
        except Exception as e:
            print(f"❌ Error fetching commits: {e}")
            return []

    def get_pipeline_status(self, commit_sha: str) -> Dict:
        """Get pipeline status for a commit"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/pipelines"
            params = {"sha": commit_sha}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            pipelines = r.json()
            
            if pipelines:
                pipeline = pipelines[0]
                return {
                    "status": pipeline["status"],
                    "id": pipeline["id"],
                    "created_at": pipeline["created_at"],
                    "updated_at": pipeline["updated_at"]
                }
            return {"status": "no pipeline", "id": None, "created_at": None, "updated_at": None}
        except Exception as e:
            print(f"❌ Error fetching pipeline status: {e}")
            return {"status": "error", "error": str(e)}

    def get_file_content(self, file_path: str, commit_sha: str = None) -> str:
        """Get file content at specific commit or latest"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/files/{requests.utils.quote(file_path, safe='')}/raw"
            params = {"ref": commit_sha or BRANCH}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            return r.text
        except Exception as e:
            print(f"❌ Error fetching file content: {e}")
            return ""

    def query_llama(self, prompt: str, context: str = "") -> str:
        """Query Llama 3 8B model via Ollama"""
        try:
            full_prompt = f"""You are an expert automotive software testing assistant with deep knowledge of:
- Automotive software development (ISO 26262, ASPICE)
- Test automation and CI/CD pipelines
- GitLab workflows and best practices
- Software quality assurance in automotive domain

Context: {context}

User Question: {prompt}

Please provide a detailed, technical response focused on automotive software testing best practices."""

            payload = {
                "model": MODEL_NAME,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9,
                    "max_tokens": 1000
                }
            }
            
            response = requests.post(f"{OLLAMA_URL}/api/generate", json=payload)
            response.raise_for_status()
            
            result = response.json()
            return result.get("response", "No response generated")
            
        except Exception as e:
            return f"Error querying Llama model: {e}"

    def analyze_test_file(self, file_path: str) -> TestAnalysis:
        """Comprehensive analysis of a test file"""
        print(f"🔍 Analyzing test file: {file_path}")
        
        # Get test cases
        test_cases = self.get_test_cases(file_path)
        
        # Get commit history
        commits = self.get_commits_for_file(file_path)
        
        # Get pipeline status for recent commits
        pipeline_status = {}
        for commit in commits[:5]:  # Analyze last 5 commits
            status = self.get_pipeline_status(commit["id"])
            pipeline_status[commit["id"]] = status
        
        # Get file content for analysis
        content = self.get_file_content(file_path)
        
        # Prepare context for LLM analysis
        context = f"""
        File: {file_path}
        Test Cases Found: {len(test_cases)}
        Test Cases: {', '.join(test_cases)}
        Recent Commits: {len(commits)}
        File Content Preview: {content[:500]}...
        Pipeline Status: {json.dumps(pipeline_status, indent=2)}
        """
        
        # Get AI analysis
        analysis_prompt = f"""
        Analyze this automotive test file and provide insights on:
        1. Test coverage and quality
        2. Potential issues or improvements
        3. Compliance with automotive standards
        4. CI/CD pipeline health
        5. Recommendations for optimization
        
        Test file details:
        {context}
        """
        
        analysis = self.query_llama(analysis_prompt, context)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(file_path, test_cases, commits, pipeline_status)
        
        return TestAnalysis(
            file_path=file_path,
            test_cases=test_cases,
            commit_history=commits,
            pipeline_status=pipeline_status,
            analysis=analysis,
            recommendations=recommendations
        )

    def _generate_recommendations(self, file_path: str, test_cases: List[str], 
                                commits: List[Dict], pipeline_status: Dict) -> List[str]:
        """Generate specific recommendations based on analysis"""
        recommendations = []
        
        # Test coverage recommendations
        if len(test_cases) < 5:
            recommendations.append("⚠️  Consider adding more test cases for better coverage")
        
        # Pipeline health recommendations
        failed_pipelines = sum(1 for status in pipeline_status.values() 
                             if status.get("status") == "failed")
        if failed_pipelines > 0:
            recommendations.append(f"🔴 {failed_pipelines} recent pipeline failures detected - investigate immediately")
        
        # Commit frequency recommendations
        if len(commits) < 3:
            recommendations.append("📝 Low commit frequency - consider more regular updates")
        
        # Automotive-specific recommendations
        recommendations.extend([
            "🚗 Ensure tests cover safety-critical functions per ISO 26262",
            "🔧 Implement regression testing for ECU integration",
            "📊 Add performance tests for real-time requirements"
        ])
        
        return recommendations

    def interactive_chat(self):
        """Interactive chat interface with the agent"""
        print("🤖 Automotive Testing Assistant - Powered by Llama 3 8B")
        print("Type 'help' for commands, 'quit' to exit")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n🔧 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'help':
                    self._show_help()
                    continue
                
                if user_input.lower().startswith('analyze '):
                    file_pattern = user_input[8:].strip()
                    self._handle_analysis_command(file_pattern)
                    continue
                
                if user_input.lower() == 'list files':
                    self._list_test_files()
                    continue
                
                if user_input.lower() == 'status':
                    self._show_project_status()
                    continue
                
                # General question - query LLM with current context
                context = json.dumps(self.current_context, indent=2) if self.current_context else ""
                response = self.query_llama(user_input, context)
                print(f"\n🤖 Assistant: {response}")
                
                # Update conversation history
                self.conversation_history.append({
                    "user": user_input,
                    "assistant": response,
                    "timestamp": datetime.now().isoformat()
                })
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

    def _show_help(self):
        """Show available commands"""
        help_text = """
🔧 Available Commands:
- help                    : Show this help message
- list files             : List all Python test files in repository
- analyze <filename>     : Analyze a specific test file
- status                 : Show overall project status
- quit/exit/q           : Exit the assistant

🔍 Ask me anything about:
- Automotive testing best practices
- CI/CD pipeline optimization
- ISO 26262 compliance
- Test automation strategies
- GitLab workflow improvements
        """
        print(help_text)

    def _list_test_files(self):
        """List all available test files"""
        files = self.get_repo_tree()
        if not files:
            print("❌ No Python files found in repository")
            return
        
        print("\n📁 Available test files:")
        for i, f in enumerate(files, 1):
            print(f"{i:2d}. {f['path']}")

    def _handle_analysis_command(self, file_pattern: str):
        """Handle the analyze command"""
        files = self.get_repo_tree()
        matching_files = [f for f in files if file_pattern.lower() in f['path'].lower()]
        
        if not matching_files:
            print(f"❌ No files found matching '{file_pattern}'")
            return
        
        if len(matching_files) == 1:
            analysis = self.analyze_test_file(matching_files[0]['path'])
            self._display_analysis(analysis)
        else:
            print(f"📁 Multiple files found matching '{file_pattern}':")
            for i, f in enumerate(matching_files, 1):
                print(f"{i:2d}. {f['path']}")
            
            try:
                choice = int(input("\n📝 Choose file number: ")) - 1
                if 0 <= choice < len(matching_files):
                    analysis = self.analyze_test_file(matching_files[choice]['path'])
                    self._display_analysis(analysis)
                else:
                    print("❌ Invalid choice")
            except ValueError:
                print("❌ Invalid input")

    def _display_analysis(self, analysis: TestAnalysis):
        """Display comprehensive analysis results"""
        print(f"\n📊 Analysis Results for: {analysis.file_path}")
        print("=" * 60)
        
        print(f"\n🔍 Test Cases Found ({len(analysis.test_cases)}):")
        for tc in analysis.test_cases:
            print(f"  • {tc}")
        
        print(f"\n📈 Recent Commits ({len(analysis.commit_history[:5])}):")
        for commit in analysis.commit_history[:5]:
            sha = commit["id"][:8]
            status = analysis.pipeline_status.get(commit["id"], {}).get("status", "unknown")
            status_emoji = {"passed": "✅", "failed": "❌", "pending": "⏳", "running": "🔄"}.get(status, "❓")
            print(f"  {status_emoji} {sha} | {status.upper()} | {commit['title']}")
        
        print(f"\n🤖 AI Analysis:")
        print(analysis.analysis)
        
        print(f"\n💡 Recommendations:")
        for rec in analysis.recommendations:
            print(f"  {rec}")
        
        # Update current context
        self.current_context = {
            "current_file": analysis.file_path,
            "test_cases": analysis.test_cases,
            "pipeline_health": analysis.pipeline_status
        }

    def _show_project_status(self):
        """Show overall project status"""
        files = self.get_repo_tree()
        print(f"\n📊 Project Status:")
        print(f"  📁 Total Python files: {len(files)}")
        
        # Quick pipeline health check
        recent_status = {}
        for file_info in files[:3]:  # Check first 3 files
            commits = self.get_commits_for_file(file_info['path'])
            if commits:
                status = self.get_pipeline_status(commits[0]['id'])
                recent_status[file_info['path']] = status['status']
        
        if recent_status:
            print(f"  🔄 Recent pipeline status:")
            for file_path, status in recent_status.items():
                status_emoji = {"passed": "✅", "failed": "❌", "pending": "⏳", "running": "🔄"}.get(status, "❓")
                print(f"    {status_emoji} {file_path}: {status.upper()}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Automotive Testing Assistant")
    parser.add_argument("--chat", action="store_true", help="Start interactive chat mode")
    parser.add_argument("--analyze", type=str, help="Analyze specific file")
    parser.add_argument("--list", action="store_true", help="List all test files")
    
    args = parser.parse_args()
    
    agent = GitLabTestingAgent()
    
    if args.chat or len(sys.argv) == 1:
        agent.interactive_chat()
    elif args.analyze:
        files = agent.get_repo_tree()
        matching_files = [f for f in files if args.analyze.lower() in f['path'].lower()]
        if matching_files:
            analysis = agent.analyze_test_file(matching_files[0]['path'])
            agent._display_analysis(analysis)
        else:
            print(f"❌ No files found matching '{args.analyze}'")
    elif args.list:
        agent._list_test_files()

if __name__ == "__main__":
    main()
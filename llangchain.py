from dotenv import load_dotenv
load_dotenv()


from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains import RetrievalQA
from langchain_mistralai.chat_models import ChatMistralAI
from langchain_community.document_loaders import PyPDFLoader
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings

import os

# Load API key
api_key = os.getenv("MISTRAL_API_KEY")

# 1. Load PDF
loader = PyPDFLoader(r"C:\Users\<USER>\Downloads\Iyed_Djebbi(4).pdf")
pages = loader.load()

# 2. Split text
splitter = RecursiveCharacterTextSplitter(chunk_size=100, chunk_overlap=50)
docs = splitter.split_documents(pages)

# 3. Embed text using HuggingFace
embedding_model = HuggingFaceEmbeddings()
vectordb = FAISS.from_documents(docs, embedding_model)

# 4. Setup Mistral LLM
llm = ChatMistralAI(
    model="mistral-tiny",  # or mistral-small, mistral-medium
    mistral_api_key=api_key,
    temperature=0.3
)

# 5. Create QA chain
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    retriever=vectordb.as_retriever(),
    return_source_documents=True
)

# 6. Ask questions
while True:
    query = input("\nAsk something about the PDF (or type 'exit'): ")
    if query.lower() == "exit":
        break
    result = qa_chain(query)
    print("\n📘 Answer:", result["result"])


from langchain_community.tools.gitlab.tool import GitLabAPIWrapper

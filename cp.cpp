class Solution {
public:
    vector<int> topKFrequent(vector<int>& nums, int k) {
        unordered_map<int,int> sortt ; 
        for (int num: nums) sortt[num]++ ; 
        set<int> tmp ; 
        for(auto pair:sortt){
            tmp.insert(pair.second)  ;
        }
        vector<int> res ; 
        auto it = tmp.rbegin();
        advance(it, k-1); 
        for(auto pair: sortt){
            if (pair.second>= *it) res.push_back(pair.first);
        }
        return res ; 
    }
};
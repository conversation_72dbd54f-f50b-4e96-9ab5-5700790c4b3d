#!/usr/bin/env python3

import requests
import re
import json
import subprocess
import sys
from datetime import datetime
from typing import List, Dict, Optional, Any, Callable
import argparse
from dataclasses import dataclass
import inspect
import os

# === Configuration ===
GITLAB_TOKEN = "**************************"
PROJECT_ID = "71217006"
BRANCH = "main"
BASE_URL = "https://gitlab.com/api/v4"
HEADERS = {"PRIVATE-TOKEN": GITLAB_TOKEN}

# Mistral API configuration
MISTRAL_API_KEY = "dMQ92i6Fl14LLU2r1UmIJf9M8JHAzgRf"  # Set this environment variable
MISTRAL_API_URL = "https://api.mistral.ai/v1/chat/completions"
MISTRAL_MODEL = "mistral-medium"  # or "mistral-small", "mistral-tiny"

@dataclass
class FunctionCall:
    """Represents a function call request from the LLM"""
    function_name: str
    parameters: Dict[str, Any]
    reasoning: str

class AutomatedTestingAgent:
    """Enhanced agent with automatic function calling capabilities using Mistral API"""
    
    def __init__(self):
        self.conversation_history = []
        self.current_context = {}
        self.available_functions = self._register_functions()
        
        # Check if Mistral API key is available
        if not MISTRAL_API_KEY:
            print("⚠️  Warning: MISTRAL_API_KEY environment variable not set!")
            print("Please set it with: export MISTRAL_API_KEY='your-api-key-here'")
        
    def _register_functions(self) -> Dict[str, Dict[str, Any]]:
        """Register available functions that the LLM can call"""
        functions = {
            "list_test_files": {
                "function": self.get_repo_tree,
                "description": "Lists all Python test files in the repository",
                "parameters": {},
                "returns": "List of test files with their paths"
            },
            "analyze_test_file": {
                "function": self.analyze_test_file,
                "description": "Performs comprehensive analysis of a specific test file including test cases, commit history, and pipeline status",
                "parameters": {
                    "file_path": "string - Path to the test file to analyze"
                },
                "returns": "Detailed analysis including test cases, commits, pipeline status, and recommendations"
            },
            "get_test_cases": {
                "function": self.get_test_cases,
                "description": "Extracts test case names from a Python test file",
                "parameters": {
                    "file_path": "string - Path to the test file"
                },
                "returns": "List of test case function names"
            },
            "get_file_content": {
                "function": self.get_file_content,
                "description": "Retrieves the content of a file at a specific commit or latest version",
                "parameters": {
                    "file_path": "string - Path to the file",
                    "commit_sha": "string (optional) - Specific commit SHA, defaults to latest"
                },
                "returns": "File content as string"
            },
            "get_pipeline_status": {
                "function": self.get_pipeline_status,
                "description": "Gets CI/CD pipeline status for a specific commit",
                "parameters": {
                    "commit_sha": "string - Commit SHA to check pipeline status for"
                },
                "returns": "Pipeline status information including status, ID, and timestamps"
            },
            "get_commits_for_file": {
                "function": self.get_commits_for_file,
                "description": "Retrieves commit history for a specific file",
                "parameters": {
                    "file_path": "string - Path to the file"
                },
                "returns": "List of commits affecting the file"
            },
            "search_files_by_pattern": {
                "function": self.search_files_by_pattern,
                "description": "Searches for files matching a pattern or containing specific keywords",
                "parameters": {
                    "pattern": "string - Search pattern or keyword to match against file paths"
                },
                "returns": "List of matching files"
            }
        }
        return functions

    def search_files_by_pattern(self, pattern: str) -> List[Dict]:
        """Search for files matching a pattern"""
        all_files = self.get_repo_tree()
        matching_files = [f for f in all_files if pattern.lower() in f['path'].lower()]
        return matching_files

    def query_mistral_with_functions(self, user_query: str, context: str = "") -> str:
        """Enhanced Mistral API query that can automatically call functions"""
        
        if not MISTRAL_API_KEY:
            return "❌ Error: Mistral API key not configured. Please set MISTRAL_API_KEY environment variable."
        
        # Create function descriptions for the prompt
        function_descriptions = "\n".join([
            f"- {name}: {info['description']}\n  Parameters: {info['parameters']}\n  Returns: {info['returns']}"
            for name, info in self.available_functions.items()
        ])
        
        system_message = f"""You are an expert automotive software testing assistant with access to GitLab repository functions.

Available Functions:
{function_descriptions}

When the user asks questions that require data from GitLab, you can call functions by responding in this JSON format:
{{
    "needs_function_call": true,
    "function_calls": [
        {{
            "function_name": "function_name",
            "parameters": {{"param1": "value1", "param2": "value2"}},
            "reasoning": "Why this function call is needed"
        }}
    ],
    "response_after_functions": "What you'll tell the user after getting the function results"
}}

If no function call is needed, respond normally with:
{{
    "needs_function_call": false,
    "response": "Your direct response to the user"
}}

Focus on automotive testing best practices, ISO 26262 compliance, and CI/CD optimization."""

        user_message = f"""Context from previous interactions: {context}
Current conversation history: {json.dumps(self.conversation_history[-3:], indent=2) if self.conversation_history else "None"}

User Query: {user_query}"""

        try:
            headers = {
                "Authorization": f"Bearer {MISTRAL_API_KEY}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": MISTRAL_MODEL,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "temperature": 0.3,
                "max_tokens": 1500,
                "top_p": 0.9
            }
            
            response = requests.post(MISTRAL_API_URL, headers=headers, json=payload)
            response.raise_for_status()
            
            result = response.json()
            llm_response = result["choices"][0]["message"]["content"]
            
            # Try to parse as JSON to check if function calls are needed
            try:
                parsed_response = json.loads(llm_response)
                
                if parsed_response.get("needs_function_call", False):
                    return self._handle_function_calls(parsed_response, user_query)
                else:
                    return parsed_response.get("response", llm_response)
                    
            except json.JSONDecodeError:
                # If not valid JSON, treat as regular response
                return llm_response
                
        except requests.exceptions.RequestException as e:
            if response.status_code == 401:
                return "❌ Error: Invalid Mistral API key. Please check your MISTRAL_API_KEY environment variable."
            elif response.status_code == 429:
                return "❌ Error: Mistral API rate limit exceeded. Please wait and try again."
            else:
                return f"❌ Error querying Mistral API: {e}"
        except Exception as e:
            return f"❌ Error querying Mistral model: {e}"

    def _handle_function_calls(self, parsed_response: Dict, original_query: str) -> str:
        """Execute function calls requested by the LLM"""
        function_calls = parsed_response.get("function_calls", [])
        function_results = []
        
        print("🔧 Executing function calls...")
        
        for call in function_calls:
            function_name = call.get("function_name")
            parameters = call.get("parameters", {})
            reasoning = call.get("reasoning", "")
            
            print(f"📋 Calling {function_name}: {reasoning}")
            
            if function_name in self.available_functions:
                try:
                    func = self.available_functions[function_name]["function"]
                    
                    # Execute the function with provided parameters
                    if parameters:
                        result = func(**parameters)
                    else:
                        result = func()
                    
                    function_results.append({
                        "function_name": function_name,
                        "parameters": parameters,
                        "result": result,
                        "success": True
                    })
                    
                except Exception as e:
                    function_results.append({
                        "function_name": function_name,
                        "parameters": parameters,
                        "error": str(e),
                        "success": False
                    })
                    print(f"❌ Error executing {function_name}: {e}")
            else:
                print(f"❌ Unknown function: {function_name}")
        
        # Now query Mistral again with the function results
        return self._generate_final_response(original_query, function_results, parsed_response)

    def _generate_final_response(self, original_query: str, function_results: List[Dict], 
                               parsed_response: Dict) -> str:
        """Generate final response using function results with Mistral API"""
        
        if not MISTRAL_API_KEY:
            return "❌ Error: Mistral API key not configured for final response generation."
        
        results_summary = json.dumps(function_results, indent=2, default=str)
        
        system_message = """Based on the function call results, provide a comprehensive response to the user's original query.

Provide a detailed, helpful response that:
1. Directly answers the user's question
2. Uses the data from function calls
3. Includes automotive testing insights
4. Provides actionable recommendations"""

        user_message = f"""Original Query: {original_query}

Function Results:
{results_summary}

Your planned response template: {parsed_response.get('response_after_functions', '')}

Please provide a comprehensive response:"""

        try:
            headers = {
                "Authorization": f"Bearer {MISTRAL_API_KEY}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": MISTRAL_MODEL,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "temperature": 0.3,
                "max_tokens": 1000,
                "top_p": 0.9
            }
            
            response = requests.post(MISTRAL_API_URL, headers=headers, json=payload)
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
            
        except Exception as e:
            return f"❌ Error generating final response with Mistral: {e}"

    # === GitLab Integration Functions (same as before) ===
    def get_repo_tree(self) -> List[Dict]:
        """Fetch all Python test files from repository"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/tree"
            params = {"ref": BRANCH, "recursive": True}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            return [f for f in r.json() if f["path"].endswith(".py")]
        except Exception as e:
            print(f"❌ Error fetching repository tree: {e}")
            return []

    def get_test_cases(self, file_path: str) -> List[str]:
        """Extract test cases from a Python file"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/files/{requests.utils.quote(file_path, safe='')}/raw"
            params = {"ref": BRANCH}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            content = r.text
            
            # Enhanced regex to capture more test patterns
            test_patterns = [
                r"def (test_\w+)\(",
                r"def (Test\w+)\(",
                r"class (Test\w+)\(",
                r"@pytest\.mark\.\w+\s*\ndef (\w+)\("
            ]
            
            test_cases = []
            for pattern in test_patterns:
                test_cases.extend(re.findall(pattern, content))
            
            return list(set(test_cases))  # Remove duplicates
        except Exception as e:
            print(f"❌ Error extracting test cases: {e}")
            return []

    def get_commits_for_file(self, file_path: str) -> List[Dict]:
        """Get commit history for a specific file"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/commits"
            params = {"path": file_path, "ref_name": BRANCH}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            return r.json()
        except Exception as e:
            print(f"❌ Error fetching commits: {e}")
            return []

    def get_pipeline_status(self, commit_sha: str) -> Dict:
        """Get pipeline status for a commit"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/pipelines"
            params = {"sha": commit_sha}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            pipelines = r.json()
            
            if pipelines:
                pipeline = pipelines[0]
                return {
                    "status": pipeline["status"],
                    "id": pipeline["id"],
                    "created_at": pipeline["created_at"],
                    "updated_at": pipeline["updated_at"],
                    "commit_sha": commit_sha
                }
            return {"status": "no pipeline", "id": None, "created_at": None, "updated_at": None, "commit_sha": commit_sha}
        except Exception as e:
            print(f"❌ Error fetching pipeline status: {e}")
            return {"status": "error", "error": str(e), "commit_sha": commit_sha}

    def get_file_content(self, file_path: str, commit_sha: str = None) -> str:
        """Get file content at specific commit or latest"""
        try:
            url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/files/{requests.utils.quote(file_path, safe='')}/raw"
            params = {"ref": commit_sha or BRANCH}
            r = requests.get(url, headers=HEADERS, params=params)
            r.raise_for_status()
            return r.text
        except Exception as e:
            print(f"❌ Error fetching file content: {e}")
            return ""

    def analyze_test_file(self, file_path: str) -> Dict:
        """Comprehensive analysis of a test file"""
        print(f"🔍 Analyzing test file: {file_path}")
        
        # Get test cases
        test_cases = self.get_test_cases(file_path)
        
        # Get commit history
        commits = self.get_commits_for_file(file_path)
        
        # Get pipeline status for recent commits
        pipeline_status = {}
        for commit in commits[:5]:  # Analyze last 5 commits
            status = self.get_pipeline_status(commit["id"])
            pipeline_status[commit["id"]] = status
        
        # Get file content for analysis
        content = self.get_file_content(file_path)
        
        return {
            "file_path": file_path,
            "test_cases": test_cases,
            "test_case_count": len(test_cases),
            "commit_history": commits[:10],  # Last 10 commits
            "pipeline_status": pipeline_status,
            "file_content_preview": content[:500] + "..." if len(content) > 500 else content,
            "analysis_timestamp": datetime.now().isoformat()
        }

    def interactive_chat(self):
        """Enhanced interactive chat with automatic function calling using Mistral"""
        print("🤖 Enhanced Automotive Testing Assistant - Powered by Mistral AI")
        print("✨ Now with automatic function calling capabilities!")
        print("Ask questions naturally - I'll automatically use GitLab functions when needed")
        print("Type 'help' for more info, 'quit' to exit")
        print("-" * 70)
        
        # Check API key status
        if not MISTRAL_API_KEY:
            print("⚠️  SETUP REQUIRED: Please set your Mistral API key:")
            print("   export MISTRAL_API_KEY='your-api-key-here'")
            print("   Get your API key from: https://console.mistral.ai/")
            print("-" * 70)
        
        # Show available functions
        print("\n🔧 Available Functions:")
        for name, info in self.available_functions.items():
            print(f"  • {name}: {info['description']}")
        
        print("\n💡 Try asking:")
        print("  • 'Show me all test files in the repository'")
        print("  • 'Analyze the brake_test.py file'")
        print("  • 'What's the pipeline status for recent commits?'")
        print("  • 'Find files related to engine testing'")
        print("-" * 70)
        
        while True:
            try:
                user_input = input("\n🔧 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'help':
                    self._show_enhanced_help()
                    continue
                
                # Use enhanced Mistral query with automatic function calling
                print("🤔 Thinking...")
                response = self.query_mistral_with_functions(user_input, json.dumps(self.current_context))
                print(f"\n🤖 Assistant: {response}")
                
                # Update conversation history
                self.conversation_history.append({
                    "user": user_input,
                    "assistant": response,
                    "timestamp": datetime.now().isoformat()
                })
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

    def _show_enhanced_help(self):
        """Show enhanced help with function calling info"""
        help_text = """
🔧 Enhanced Assistant Capabilities:

🤖 Automatic Function Execution:
The assistant can now automatically call GitLab functions based on your questions!

📋 Available Functions:
- list_test_files: Lists all Python test files
- analyze_test_file: Comprehensive file analysis
- get_test_cases: Extract test cases from files
- get_file_content: Retrieve file contents
- get_pipeline_status: Check CI/CD pipeline status
- get_commits_for_file: Get commit history
- search_files_by_pattern: Search for specific files

💬 Natural Language Examples:
- "Show me all test files" → Automatically calls list_test_files()
- "Analyze brake_test.py" → Calls analyze_test_file("brake_test.py")
- "What test cases are in engine_test.py?" → Calls get_test_cases()
- "Find files related to transmission" → Calls search_files_by_pattern()

🎯 Automotive Testing Focus:
- ISO 26262 compliance guidance
- Safety-critical testing best practices
- CI/CD pipeline optimization
- Automotive standards integration

🔑 API Configuration:
- Model: Mistral Medium (configurable)
- API Key: Set via MISTRAL_API_KEY environment variable
- Get your key: https://console.mistral.ai/

Commands:
- help: Show this help
- quit/exit/q: Exit assistant
        """
        print(help_text)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Enhanced Automotive Testing Assistant with Mistral AI")
    parser.add_argument("--chat", action="store_true", help="Start interactive chat mode")
    parser.add_argument("--model", default="mistral-medium", 
                        choices=["mistral-tiny", "mistral-small", "mistral-medium", "mistral-large"],
                        help="Mistral model to use")
    
    args = parser.parse_args()
    
    # Update global model if specified
    global MISTRAL_MODEL
    MISTRAL_MODEL = args.model
    
    agent = AutomatedTestingAgent()
    
    if args.chat or len(sys.argv) == 1:
        agent.interactive_chat()

if __name__ == "__main__":
    main()
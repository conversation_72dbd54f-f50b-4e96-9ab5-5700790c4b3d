'''import requests
import json
import base64
import hashlib
from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, START, END
from langgraph.graph import Graph
from pydantic import BaseModel
from langchain_core.tools import tool

# === Configuration ===
GITLAB_TOKEN = "**************************"
PROJECT_ID = "71217006"
BRANCH = "main"
BASE_URL = "https://gitlab.com/api/v4"
HEADERS = {"PRIVATE-TOKEN": GITLAB_TOKEN}

# Mistral API configuration
MISTRAL_API_KEY = "dMQ92i6Fl14LLU2r1UmIJf9M8JHAzgRf"
MISTRAL_API_URL = "https://api.mistral.ai/v1/chat/completions"
MISTRAL_MODEL = "mistral-medium"

class FileComparisonState(BaseModel):
    """State for the file comparison workflow"""
    commit_ref_1: str = ""
    commit_ref_2: str = ""
    file_name: str = ""
    file_1_content: str = ""
    file_2_content: str = ""
    file_1_hash: str = ""
    file_2_hash: str = ""
    files_are_different: bool = False
    comparison_result: str = ""
    error: str = ""

def get_user_input_node(state: FileComparisonState) -> FileComparisonState:
    """Get commit refs and filename from user"""
    print("=== GitLab File Comparison Tool ===")
    state.commit_ref_1 = input("Enter first commit ref (SHA/branch/tag): ").strip()
    state.commit_ref_2 = input("Enter second commit ref (SHA/branch/tag): ").strip()
    state.file_name = input("Enter filename to compare: ").strip()
    return state

def fetch_file_content(commit_ref: str, file_path: str) -> tuple[str, str]:
    """Fetch file content and calculate hash from a specific commit"""
    try:
        # URL encode the file path
        encoded_file_path = file_path.replace('/', '%2F')
        file_url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/files/{encoded_file_path}"
        file_params = {"ref": commit_ref}
        
        file_response = requests.get(file_url, headers=HEADERS, params=file_params)
        file_response.raise_for_status()
        
        file_data = file_response.json()
        
        # Decode content
        if file_data.get('encoding') == 'base64':
            try:
                content = base64.b64decode(file_data['content']).decode('utf-8')
            except UnicodeDecodeError:
                raise Exception(f"Cannot decode file {file_path} - it may be a binary file")
        else:
            content = file_data.get('content', '')
        
        # Calculate hash of the content
        content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()
        
        return content, content_hash
        
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            raise Exception(f"File '{file_path}' not found in commit {commit_ref}")
        else:
            raise Exception(f"HTTP error fetching file: {e}")
    except Exception as e:
        raise Exception(f"Error fetching file '{file_path}' from commit {commit_ref}: {e}")

def fetch_file_node(state: FileComparisonState) -> FileComparisonState:
    """Fetch file from both commits and compare hashes"""
    try:
        print(f"Fetching '{state.file_name}' from commit {state.commit_ref_1}...")
        state.file_1_content, state.file_1_hash = fetch_file_content(state.commit_ref_1, state.file_name)
        
        print(f"Fetching '{state.file_name}' from commit {state.commit_ref_2}...")
        state.file_2_content, state.file_2_hash = fetch_file_content(state.commit_ref_2, state.file_name)
        
        # Compare hashes
        state.files_are_different = state.file_1_hash != state.file_2_hash
        
        if state.files_are_different:
            print(f"✅ File has changed between commits (hash mismatch)")
            print(f"Commit 1 hash: {state.file_1_hash[:12]}...")
            print(f"Commit 2 hash: {state.file_2_hash[:12]}...")
        else:
            print(f"ℹ️  File is identical in both commits")
            state.comparison_result = "The file is identical in both commits - no changes detected."
        
    except Exception as e:
        state.error = str(e)
    
    return state

def compare_with_llm_node(state: FileComparisonState) -> FileComparisonState:
    """Send files to Mistral for line-by-line comparison"""
    if state.error or not state.files_are_different:
        return state
    
    try:
        # Prepare the comparison prompt
        prompt = f"""
I have two versions of the same file from different commits that I need you to compare:

FILENAME: {state.file_name}

VERSION 1 (from commit {state.commit_ref_1}):
```
{state.file_1_content}
```

VERSION 2 (from commit {state.commit_ref_2}):
```
{state.file_2_content}
```

Please analyze these two versions and provide:
1. **Line-by-line differences**: Identify the exact lines that changed, were added, or were removed
2. **Specific changes**: What exactly was modified in each changed line
3. **Line numbers**: Reference the line numbers where changes occurred
4. **Change summary**: Brief description of what type of changes were made (e.g., function modifications, variable changes, etc.)

Focus on being precise about the exact lines and changes. Format your response clearly showing:
- Lines that were modified (show before/after)
- Lines that were added (show line number and content)
- Lines that were removed (show line number and content)
"""
        
        # Call Mistral API
        print("Sending to Mistral AI for line-by-line analysis...")
        payload = {
            "model": MISTRAL_MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,  # Lower temperature for more precise analysis
            "max_tokens": 2000
        }
        
        headers = {
            "Authorization": f"Bearer {MISTRAL_API_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(MISTRAL_API_URL, json=payload, headers=headers)
        response.raise_for_status()
        
        state.comparison_result = response.json()["choices"][0]["message"]["content"]
        
    except Exception as e:
        state.error = f"Error calling Mistral API: {e}"
    
    return state

def output_result_node(state: FileComparisonState) -> FileComparisonState:
    """Output the comparison result"""
    if state.error:
        print(f"\n❌ Error: {state.error}")
    else:
        print(f"\n{'='*80}")
        print(f"FILE COMPARISON ANALYSIS")
        print(f"File: {state.file_name}")
        print(f"Commit 1: {state.commit_ref_1}")
        print(f"Commit 2: {state.commit_ref_2}")
        print(f"{'='*80}")
        print(state.comparison_result)
    
    return state

def create_comparison_workflow() -> Graph:
    """Create the LangGraph workflow"""
    workflow = StateGraph(FileComparisonState)
    
    # Add nodes
    workflow.add_node("get_input", get_user_input_node)
    workflow.add_node("fetch_file", fetch_file_node)
    workflow.add_node("compare_llm", compare_with_llm_node)
    workflow.add_node("output_result", output_result_node)
    
    # Define the flow
    workflow.add_edge(START, "get_input")
    workflow.add_edge("get_input", "fetch_file")
    workflow.add_edge("fetch_file", "compare_llm")
    workflow.add_edge("compare_llm", "output_result")
    workflow.add_edge("output_result", END)
    
    # Compile the graph
    compiled_graph = workflow.compile()    
    return compiled_graph

@tool
def gitlab_file_comparison_tool():
    """
    Interactive tool that asks for two commit refs and a filename, checks if the file
    has changed by comparing hashes, and if different, uses Mistral AI to identify
    the exact lines where changes occurred.
    """
    app = create_comparison_workflow()
    initial_state = FileComparisonState()
    result = app.invoke(initial_state)
    return result.comparison_result if not result.error else f"Error: {result.error}"

# === Main execution ===
if __name__ == "__main__":
    # Run the interactive file comparison
    app = create_comparison_workflow()
    initial_state = FileComparisonState()
    app.invoke(initial_state)'''



from langchain_community.tools.gitlab.tool import GitLabAction
from langchain_community.utilities.gitlab import GitLabAPIWrapper
import re
import requests
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from langgraph.graph import Graph, StateGraph, END
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage
from langchain_core.tools import tool
from pydantic import BaseModel, Field
import json
from tools import make_gitlab_tools, create_gitlab_config, GitLabAPIError

# Mistral API configuration
MISTRAL_API_KEY = "dMQ92i6Fl14LLU2r1UmIJf9M8JHAzgRf"
MISTRAL_API_URL = "https://api.mistral.ai/v1/chat/completions"
MISTRAL_MODEL = "mistral-medium"  # or "mistral-small", "mistral-tiny"


class AgentState(BaseModel):
    """State for the GitLab agent"""
    messages: List[BaseMessage] = Field(default_factory=list)
    user_input: str = ""
    extracted_info: Dict[str, Any] = Field(default_factory=dict)
    current_step: str = "analyze_input"
    file_sha: Optional[str] = None
    file_content: Optional[str] = None
    response: str = ""


class MistralLLM:
    """Simple Mistral API wrapper"""
    
    def __init__(self, api_key: str, model: str = "mistral-medium"):
        self.api_key = api_key
        self.model = model
        self.base_url = MISTRAL_API_URL
    
    def invoke(self, messages: List[Dict[str, str]], temperature: float = 0) -> str:
        """Make a request to Mistral API"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            return f"Error calling Mistral API: {str(e)}"


class GitLabAgent:
    """
    LangGraph agent for GitLab file operations.
    Handles user queries and determines which GitLab functions to call.
    """
    
    def __init__(self, gitlab_config: Dict[str, str], mistral_api_key: str = MISTRAL_API_KEY, mistral_model: str = MISTRAL_MODEL):
        self.config = gitlab_config
        self.llm = MistralLLM(mistral_api_key, mistral_model)
        
        # Create GitLab tools
        self.setup_tools()
        
        # Create the graph
        self.graph = self.create_graph()
    
    def setup_tools(self):
        """Setup GitLab tools for the agent"""
        get_sha_func, get_sha_ts_func, get_content_func = make_gitlab_tools(self.config)
        
        @tool
        def get_file_sha_tool(commit_ref: str, file_path: str) -> str:
            """Get SHA hash of a file at a specific commit reference"""
            try:
                return get_sha_func(commit_ref, file_path)
            except GitLabAPIError as e:
                return f"Error: {str(e)}"
        
        @tool
        def get_file_sha_from_branch_ts_tool(branch: str, timestamp: str, file_path: str) -> str:
            """Get SHA hash of a file from latest commit on branch before timestamp"""
            try:
                return get_sha_ts_func(branch, timestamp, file_path)
            except GitLabAPIError as e:
                return f"Error: {str(e)}"
        
        @tool
        def get_file_content_tool(file_sha: str) -> str:
            """Get file content using SHA hash"""
            try:
                return get_content_func(file_sha)
            except GitLabAPIError as e:
                return f"Error: {str(e)}"
        
        self.tools = [
            get_file_sha_tool,
            get_file_sha_from_branch_ts_tool,
            get_file_content_tool
        ]
        
        # Create a tool mapping for easier access
        self.tool_map = {
            "get_file_sha": get_file_sha_tool,
            "get_file_sha_from_branch_ts": get_file_sha_from_branch_ts_tool,
            "get_file_content": get_file_content_tool
        }
    
    def execute_tool(self, tool_name: str, **kwargs) -> str:
        """Execute a tool by name with given arguments"""
        if tool_name not in self.tool_map:
            return f"Error: Unknown tool '{tool_name}'"
        
        tool = self.tool_map[tool_name]
        try:
            return tool.func(**kwargs)
        except Exception as e:
            return f"Error executing {tool_name}: {str(e)}"
    
    def analyze_input_with_llm(self, user_input: str) -> Dict[str, Any]:
        """Use Mistral to help analyze complex user input"""
        prompt = f"""
        Analyze the following user input and extract relevant information for GitLab file operations:
        
        User Input: "{user_input}"
        
        Please identify and extract:
        1. SHA hash (40 character hex string or 7+ character short hash)
        2. Branch name (after words like "branch", "on", "from")
        3. Timestamp (ISO format, date, or relative time)
        4. File path (file names with extensions or paths)
        5. Commit reference
        6. Intent (what the user wants to do: get_content, get_sha, etc.)
        
        Return ONLY a JSON object with the extracted information:
        {{
            "sha": "extracted_sha_or_null",
            "branch": "extracted_branch_or_null", 
            "timestamp": "extracted_timestamp_or_null",
            "file_path": "extracted_file_path_or_null",
            "commit_ref": "extracted_commit_ref_or_null",
            "intent": "predicted_intent"
        }}
        """
        
        messages = [{"role": "user", "content": prompt}]
        
        try:
            response = self.llm.invoke(messages)
            # Try to parse JSON from response
            # Clean up the response in case there's extra text
            response = response.strip()
            if '```json' in response:
                response = response.split('```json')[1].split('```')[0].strip()
            elif '```' in response:
                response = response.split('```')[1].split('```')[0].strip()
            
            return json.loads(response)
        except Exception as e:
            print(f"LLM analysis failed: {e}")
            return {}
    
    def analyze_input(self, state: AgentState) -> AgentState:
        """Analyze user input to extract relevant information"""
        user_input = state.user_input
        
        # First try regex patterns for quick extraction
        patterns = {
            'sha': r'([a-f0-9]{40}|[a-f0-9]{7,})',  # SHA hash (full or short)
            'branch': r'(?:branch|on|from)\s+([a-zA-Z0-9_\-\/]+)',
            'timestamp': r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z?|\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4})',
            'file_path': r'(?:file|path)\s+([a-zA-Z0-9_\-\/\.]+)|([a-zA-Z0-9_\-\/\.]+\.[a-zA-Z]+)',
            'commit_ref': r'(?:commit|ref)\s+([a-zA-Z0-9_\-\/]+)',
        }
        
        extracted = {}
        
        for key, pattern in patterns.items():
            match = re.search(pattern, user_input, re.IGNORECASE)
            if match:
                extracted[key] = match.group(1) if match.group(1) else match.group(2)
        
        # Try to infer file path from common extensions if not explicitly found
        if 'file_path' not in extracted:
            file_extensions = r'([a-zA-Z0-9_\-\/]+\.(?:py|js|json|md|txt|yml|yaml|xml|html|css|java|cpp|c|h))'
            match = re.search(file_extensions, user_input, re.IGNORECASE)
            if match:
                extracted['file_path'] = match.group(1)
        
        # Use LLM for more complex analysis if basic patterns don't yield enough info
        if len(extracted) < 2:  # If we don't have enough info, try LLM
            llm_extracted = self.analyze_input_with_llm(user_input)
            # Merge LLM results with regex results, preferring non-null values
            for key, value in llm_extracted.items():
                if value and value != "null" and key not in extracted:
                    extracted[key] = value
        
        # Determine the intent
        intent = self.determine_intent(user_input, extracted)
        extracted['intent'] = intent
        
        state.extracted_info = extracted
        state.current_step = "execute_action"
        
        return state
    
    def determine_intent(self, user_input: str, extracted: Dict[str, Any]) -> str:
        """Determine what the user wants to do based on input and extracted info"""
        input_lower = user_input.lower()
        
        # Check for content-related queries
        if any(word in input_lower for word in ['content', 'show', 'read', 'display', 'what', 'contains']):
            if 'sha' in extracted:
                return 'get_content_from_sha'
            elif 'branch' in extracted and 'file_path' in extracted:
                if 'timestamp' in extracted:
                    return 'get_content_from_branch_timestamp'
                else:
                    return 'get_content_from_branch'
            else:
                return 'need_more_info'
        
        # Check for SHA-related queries
        elif any(word in input_lower for word in ['sha', 'hash', 'id']):
            if 'branch' in extracted and 'file_path' in extracted:
                if 'timestamp' in extracted:
                    return 'get_sha_from_branch_timestamp'
                else:
                    return 'get_sha_from_branch'
            else:
                return 'need_more_info'
        
        # Default: try to get content if we have enough info
        elif 'file_path' in extracted:
            if 'sha' in extracted:
                return 'get_content_from_sha'
            elif 'branch' in extracted:
                if 'timestamp' in extracted:
                    return 'get_content_from_branch_timestamp'
                else:
                    return 'get_content_from_branch'
        
        return 'need_more_info'
    
    def execute_action(self, state: AgentState) -> AgentState:
        """Execute the appropriate GitLab action based on intent"""
        intent = state.extracted_info.get('intent')
        extracted = state.extracted_info
        
        try:
            if intent == 'get_content_from_sha':
                sha = extracted.get('sha')
                if sha:
                    content = self.execute_tool('get_file_content', file_sha=sha)
                    state.file_content = content
                    state.response = f"File content for SHA {sha}:\n\n{content}"
                else:
                    state.response = "Error: No SHA hash found in your input."
            
            elif intent == 'get_content_from_branch':
                branch = extracted.get('branch', 'main')
                file_path = extracted.get('file_path')
                if file_path:
                    # First get SHA, then get content
                    sha = self.execute_tool('get_file_sha', commit_ref=branch, file_path=file_path)
                    if not sha.startswith('Error:'):
                        content = self.execute_tool('get_file_content', file_sha=sha)
                        state.file_content = content
                        state.response = f"File content for '{file_path}' on branch '{branch}':\n\n{content}"
                    else:
                        state.response = sha
                else:
                    state.response = "Error: No file path found in your input."
            
            elif intent == 'get_content_from_branch_timestamp':
                branch = extracted.get('branch', 'main')
                timestamp = extracted.get('timestamp')
                file_path = extracted.get('file_path')
                
                if file_path and timestamp:
                    # Normalize timestamp format
                    timestamp = self.normalize_timestamp(timestamp)
                    sha = self.execute_tool('get_file_sha_from_branch_ts', 
                                          branch=branch, timestamp=timestamp, file_path=file_path)
                    if not sha.startswith('Error:'):
                        content = self.execute_tool('get_file_content', file_sha=sha)
                        state.file_content = content
                        state.response = f"File content for '{file_path}' on branch '{branch}' before {timestamp}:\n\n{content}"
                    else:
                        state.response = sha
                else:
                    state.response = "Error: Missing file path or timestamp in your input."
            
            elif intent == 'get_sha_from_branch':
                branch = extracted.get('branch', 'main')
                file_path = extracted.get('file_path')
                if file_path:
                    sha = self.execute_tool('get_file_sha', commit_ref=branch, file_path=file_path)
                    state.file_sha = sha
                    state.response = f"SHA for '{file_path}' on branch '{branch}': {sha}"
                else:
                    state.response = "Error: No file path found in your input."
            
            elif intent == 'get_sha_from_branch_timestamp':
                branch = extracted.get('branch', 'main')
                timestamp = extracted.get('timestamp')
                file_path = extracted.get('file_path')
                
                if file_path and timestamp:
                    timestamp = self.normalize_timestamp(timestamp)
                    sha = self.execute_tool('get_file_sha_from_branch_ts', 
                                          branch=branch, timestamp=timestamp, file_path=file_path)
                    state.file_sha = sha
                    state.response = f"SHA for '{file_path}' on branch '{branch}' before {timestamp}: {sha}"
                else:
                    state.response = "Error: Missing file path or timestamp in your input."
            
            else:
                state.response = self.generate_help_message(extracted)
        
        except Exception as e:
            state.response = f"Error executing action: {str(e)}"
        
        state.current_step = "generate_response"
        return state
    
    def normalize_timestamp(self, timestamp: str) -> str:
        """Normalize timestamp to ISO format"""
        # Handle different timestamp formats
        if '/' in timestamp:
            # MM/DD/YYYY format
            try:
                dt = datetime.strptime(timestamp, '%m/%d/%Y')
                return dt.strftime('%Y-%m-%dT00:00:00Z')
            except ValueError:
                pass
        elif 'T' not in timestamp and len(timestamp) == 10:
            # YYYY-MM-DD format
            return f"{timestamp}T00:00:00Z"
        elif not timestamp.endswith('Z'):
            # Add Z if missing
            return f"{timestamp}Z"
        
        return timestamp
    
    def generate_help_message(self, extracted: Dict[str, Any]) -> str:
        """Generate helpful message when intent is unclear"""
        available_info = [k for k in extracted.keys() if k != 'intent']
        
        message = "I can help you with GitLab file operations. "
        
        if available_info:
            message += f"I found: {', '.join(available_info)} in your input. "
        
        message += """
Here are some examples of what you can ask:

1. Get file content:
   - "Show me the content of README.md from main branch"
   - "What's in config.py on develop branch?"
   - "Display the content of file with SHA abc123def456"

2. Get file SHA:
   - "Get SHA for utils.py on main branch"
   - "What's the hash of database.py on feature-branch before 2024-01-15T10:30:00Z?"

3. Historical queries:
   - "Show me app.py from main branch as it was on 2024-01-10"
   - "Get the content of config.json from develop branch before 01/15/2024"

Please provide more specific information about what you'd like to do.
"""
        return message
    
    def generate_response(self, state: AgentState) -> AgentState:
        """Generate final response with optional LLM enhancement"""
        # For simple successful responses, return as-is
        if not state.response.startswith("Error:") and "need_more_info" not in state.extracted_info.get('intent', ''):
            state.current_step = "complete"
            return state
        
        # For complex responses or errors, enhance with LLM
        prompt = f"""
        Based on the following context, provide a helpful and clear response to the user:
        
        User Input: "{state.user_input}"
        Extracted Information: {state.extracted_info}
        Current Response: "{state.response}"
        
        Please provide a clear, helpful response that:
        1. Addresses the user's request
        2. Explains what was found or what went wrong
        3. Suggests next steps if needed
        4. Is conversational and user-friendly
        
        Keep the response concise but informative.
        """
        
        messages = [{"role": "user", "content": prompt}]
        
        try:
            enhanced_response = self.llm.invoke(messages)
            state.response = enhanced_response
        except Exception as e:
            # If LLM fails, keep the original response
            print(f"Response enhancement failed: {e}")
        
        state.current_step = "complete"
        return state
    
    def create_graph(self) -> Graph:
        """Create the LangGraph workflow"""
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("analyze_input", self.analyze_input)
        workflow.add_node("execute_action", self.execute_action)
        workflow.add_node("generate_response", self.generate_response)
        
        # Add edges
        workflow.add_edge("analyze_input", "execute_action")
        workflow.add_edge("execute_action", "generate_response")
        workflow.add_edge("generate_response", END)
        
        # Set entry point
        workflow.set_entry_point("analyze_input")
        
        return workflow.compile()
    
    def process_query(self, user_input: str) -> str:
        """Process a user query and return response"""
        initial_state = AgentState(user_input=user_input)
        
        # Run the graph
        final_state = self.graph.invoke(initial_state)
        
        return final_state["response"]


# Example usage and testing
def main():
    # Configuration
    config = create_gitlab_config(
        gitlab_url="https://gitlab.com",
        project_id="71217006",
        token="**************************" 
    )
    
    # Create agent with Mistral
    agent = GitLabAgent(config, mistral_api_key=MISTRAL_API_KEY, mistral_model=MISTRAL_MODEL)
    
    # Example queries
    test_queries = [
        "Show me the content of .gitlab-ci.yml from main branch",
        "What's the SHA of .gitlab-ci.yml on main branch?",
        "Get the content of .gitlab-ci.yml from main branch as it was on 2025-08-7T10:30:00Z",
    ]
    
    print("GitLab Agent with Mistral - Interactive Demo")
    print("=" * 50)
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        print("-" * 30)
        response = agent.process_query(query)
        print(f"Response: {response}")
        print()
    
    # Interactive mode
    print("\n" + "=" * 50)
    print("Interactive Mode - Type 'quit' to exit")
    print("=" * 50)
    
    while True:
        try:
            user_input = input("\nYour query: ")
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            response = agent.process_query(user_input)
            print(f"\nResponse: {response}")
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")


# Additional utility function for batch processing
def process_multiple_queries(queries: List[str], config: Dict[str, str]) -> List[Dict[str, str]]:
    """Process multiple queries and return results"""
    agent = GitLabAgent(config, mistral_api_key=MISTRAL_API_KEY, mistral_model=MISTRAL_MODEL)
    
    results = []
    for query in queries:
        try:
            response = agent.process_query(query)
            results.append({
                "query": query,
                "response": response,
                "status": "success"
            })
        except Exception as e:
            results.append({
                "query": query,
                "response": f"Error: {str(e)}",
                "status": "error"
            })
    
    return results


if __name__ == "__main__":
    main()
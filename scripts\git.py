import requests
import re

# === Replace with your credentials ===
GITLAB_TOKEN = "**************************"
PROJECT_ID = "71217006"  # e.g., 71217006
BRANCH = "main"

BASE_URL = "https://gitlab.com/api/v4"
HEADERS = {"PRIVATE-TOKEN": GITLAB_TOKEN}

def get_repo_tree():
    url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/tree"
    params = {"ref": BRANCH, "recursive": True}
    r = requests.get(url, headers=HEADERS, params=params)
    r.raise_for_status()
    return [f for f in r.json() if f["path"].endswith(".py")]

def get_test_cases(file_path):
    url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/files/{requests.utils.quote(file_path, safe='')}/raw"
    params = {"ref": BRANCH}
    r = requests.get(url, headers=HEADERS, params=params)
    r.raise_for_status()
    content = r.text
    return re.findall(r"def (test_\w+)\(", content)

def get_commits_for_file(file_path):
    url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/commits"
    params = {"path": file_path, "ref_name": BRANCH}
    r = requests.get(url, headers=HEADERS, params=params)
    r.raise_for_status()
    return r.json()

def get_pipeline_status(commit_sha):
    url = f"{BASE_URL}/projects/{PROJECT_ID}/pipelines"
    params = {"sha": commit_sha}
    r = requests.get(url, headers=HEADERS, params=params)
    r.raise_for_status()
    pipelines = r.json()
    return pipelines[0]["status"] if pipelines else "no pipeline"

def get_file_content(file_path, commit_sha):
    url = f"{BASE_URL}/projects/{PROJECT_ID}/repository/files/{requests.utils.quote(file_path, safe='')}/raw"
    params = {"ref": commit_sha}
    r = requests.get(url, headers=HEADERS, params=params)
    r.raise_for_status()
    return r.text

def main():
    print("📁 Fetching Python test files...")
    files = get_repo_tree()
    if not files:
        print("❌ No Python files found.")
        return

    for i, f in enumerate(files):
        print(f"{i + 1}. {f['path']}")

    choice = int(input("\n📝 Choose a file (number): ")) - 1
    selected_file = files[choice]["path"]
    print(f"\n📄 Selected: {selected_file}")

    test_cases = get_test_cases(selected_file)
    if not test_cases:
        print("❌ No test cases found.")
        return

    print("\n🔍 Test Cases:")
    for i, tc in enumerate(test_cases):
        print(f"{i + 1}. {tc}")

    case_choice = int(input("\n📌 Pick a test case (number): ")) - 1
    selected_test = test_cases[case_choice]
    print(f"\n🎯 Tracking test case: {selected_test}")

    print("\n🕒 Getting commit history...")
    commits = get_commits_for_file(selected_file)
    for commit in commits:
        sha = commit["id"]
        msg = commit["title"]
        status = get_pipeline_status(sha)
        print(f"- {sha[:8]} | {status.upper()} | {msg}")

    while True:
        view = input("\n👁 View test file at specific commit? (enter commit SHA or 'x' to exit): ").strip()
        if view.lower() == "x":
            print("👋 Exiting viewer.")
            break
        try:
            content = get_file_content(selected_file, view)
            print(f"\n📄 Code from {view[:8]}:\n{'-'*50}\n{content}")
        except Exception as e:
            print(f"❌ Failed to fetch code: {e}")


main()

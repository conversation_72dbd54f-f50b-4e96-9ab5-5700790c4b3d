import os
import requests
import base64
from typing import List, Dict, Any, Optional
import json
from datetime import datetime
import tiktoken

# === Configuration ===
GITLAB_TOKEN = "**************************"
PROJECT_ID = "71217006"
BRANCH = "main"
BASE_URL = "https://gitlab.com/api/v4"
HEADERS = {"PRIVATE-TOKEN": GITLAB_TOKEN}

# Mistral API configuration
MISTRAL_API_KEY = "dMQ92i6Fl14LLU2r1UmIJf9M8JHAzgRf"
MISTRAL_API_URL = "https://api.mistral.ai/v1/chat/completions"
MISTRAL_MODEL = "mistral-medium"

# Token limits for different models
TOKEN_LIMITS = {
    "mistral-medium": 32000,  # Conservative estimate
    "mistral-large": 32000,
    "mistral-small": 32000,
}

class TokenCounter:
    """Token counting utility"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        # Use GPT tokenizer as approximation for Mistral
        try:
            self.encoding = tiktoken.encoding_for_model(model_name)
        except:
            self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        return len(self.encoding.encode(text))
    
    def count_messages_tokens(self, messages: List[Dict[str, str]]) -> int:
        """Count tokens in a list of messages"""
        total = 0
        for message in messages:
            # Account for message formatting overhead
            total += 4  # Base tokens per message
            for key, value in message.items():
                total += self.count_tokens(str(value))
        return total
    
    def truncate_text(self, text: str, max_tokens: int) -> str:
        """Truncate text to fit within token limit"""
        if self.count_tokens(text) <= max_tokens:
            return text
        
        # Binary search for the right length
        tokens = self.encoding.encode(text)
        if len(tokens) <= max_tokens:
            return text
        
        truncated_tokens = tokens[:max_tokens]
        return self.encoding.decode(truncated_tokens)

class GitLabRepository:
    """GitLab repository interface"""
    
    def __init__(self, token: str, project_id: str, branch: str = "main"):
        self.token = token
        self.project_id = project_id
        self.branch = branch
        self.base_url = BASE_URL
        self.headers = {"PRIVATE-TOKEN": token}
        self.project_info = None
        self.file_cache = {}
    
    def get_project_info(self) -> Dict[str, Any]:
        """Get basic project information"""
        try:
            url = f"{self.base_url}/projects/{self.project_id}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            self.project_info = response.json()
            return self.project_info
        except Exception as e:
            raise Exception(f"Error fetching project info: {str(e)}")
    
    def get_repository_structure(self, path: str = "") -> List[Dict[str, Any]]:
        """Get repository file structure"""
        try:
            url = f"{self.base_url}/projects/{self.project_id}/repository/tree"
            params = {"ref": self.branch, "path": path, "recursive": True}
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            return response.json()
        except Exception as e:
            raise Exception(f"Error fetching repository structure: {str(e)}")
    
    def get_file_content(self, file_path: str) -> Dict[str, Any]:
        """Get content of a specific file"""
        try:
            # Check cache first
            if file_path in self.file_cache:
                return self.file_cache[file_path]
            
            encoded_path = file_path.replace('/', '%2F')
            url = f"{self.base_url}/projects/{self.project_id}/repository/files/{encoded_path}"
            params = {"ref": self.branch}
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            file_data = response.json()
            content = base64.b64decode(file_data['content']).decode('utf-8')
            file_data['decoded_content'] = content
            
            # Cache the result
            self.file_cache[file_path] = file_data
            return file_data
        except Exception as e:
            raise Exception(f"Error fetching file content for {file_path}: {str(e)}")
    
    def get_recent_commits(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent commits"""
        try:
            url = f"{self.base_url}/projects/{self.project_id}/repository/commits"
            params = {"ref_name": self.branch, "per_page": limit}
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            return response.json()
        except Exception as e:
            raise Exception(f"Error fetching commits: {str(e)}")
    
    def search_files(self, query: str) -> List[str]:
        """Search for files matching query terms"""
        try:
            files = self.get_repository_structure()
            relevant_files = []
            query_lower = query.lower()
            
            for file in files:
                if file.get('type') == 'blob':
                    file_path = file.get('path', '').lower()
                    file_name = file.get('name', '').lower()
                    
                    # Check if query terms are in file path or name
                    if any(term in file_path or term in file_name for term in query_lower.split()):
                        relevant_files.append(file.get('path'))
            
            return relevant_files
        except Exception as e:
            raise Exception(f"Error searching files: {str(e)}")

class MistralAI:
    """Mistral AI interface"""
    
    def __init__(self, api_key: str, model: str = "mistral-medium"):
        self.api_key = api_key
        self.model = model
        self.api_url = MISTRAL_API_URL
        self.token_counter = TokenCounter()
    
    def chat(self, messages: List[Dict[str, str]], max_tokens: int = 2000, temperature: float = 0.7) -> str:
        """Send a chat completion request to Mistral"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # Count tokens before sending
        input_tokens = self.token_counter.count_messages_tokens(messages)
        print(f"📊 Token Count: {input_tokens} input tokens")
        
        # Check if we're approaching the limit
        model_limit = TOKEN_LIMITS.get(self.model, 32000)
        if input_tokens + max_tokens > model_limit:
            print(f"⚠️  Warning: Total tokens ({input_tokens + max_tokens}) may exceed model limit ({model_limit})")
        
        data = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        try:
            response = requests.post(self.api_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            
            # Try to get usage info if available
            if 'usage' in result:
                usage = result['usage']
                print(f"📊 API Usage: {usage.get('prompt_tokens', 'N/A')} prompt + {usage.get('completion_tokens', 'N/A')} completion = {usage.get('total_tokens', 'N/A')} total tokens")
            
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"❌ API Error: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            raise Exception(f"Error calling Mistral API: {str(e)}")

class GitLabRepositoryAgent:
    """Main agent class for GitLab repository Q&A"""
    
    def __init__(self):
        self.gitlab = GitLabRepository(GITLAB_TOKEN, PROJECT_ID, BRANCH)
        self.mistral = MistralAI(MISTRAL_API_KEY, MISTRAL_MODEL)
        self.token_counter = TokenCounter()
        self.conversation_history = []
        self.repository_context = None
    
    def _initialize_context(self):
        """Initialize repository context"""
        if self.repository_context is None:
            try:
                # Get project info
                project_info = self.gitlab.get_project_info()
                
                # Get repository structure
                structure = self.gitlab.get_repository_structure()
                
                # Get recent commits
                commits = self.gitlab.get_recent_commits(5)
                
                # Try to get README content
                readme_content = ""
                readme_files = ["README.md", "README.txt", "README.rst", "readme.md"]
                for readme_file in readme_files:
                    try:
                        readme_data = self.gitlab.get_file_content(readme_file)
                        readme_content = readme_data.get('decoded_content', '')
                        break
                    except:
                        continue
                
                # Build context
                self.repository_context = {
                    'project_info': project_info,
                    'structure': structure,
                    'commits': commits,
                    'readme': readme_content
                }
            except Exception as e:
                print(f"Warning: Could not initialize full context: {e}")
                self.repository_context = {}
    
    def _format_project_info(self, project_info: Dict[str, Any]) -> str:
        """Format project information for display"""
        return f"""Project: {project_info.get('name', 'N/A')}
Description: {project_info.get('description', 'N/A')}
Language: {project_info.get('language', 'N/A')}
Stars: {project_info.get('star_count', 0)}
Last Activity: {project_info.get('last_activity_at', 'N/A')}"""
    
    def _format_repository_structure(self, structure: List[Dict[str, Any]], limit: int = 30) -> str:
        """Format repository structure for display"""
        result = f"Repository Structure ({len(structure)} files):\n"
        
        for file in structure[:limit]:
            file_type = file.get('type', 'blob')
            file_path = file.get('path', '')
            
            if file_type == 'tree':
                result += f"📁 {file_path}/\n"
            else:
                result += f"📄 {file_path}\n"
        
        if len(structure) > limit:
            result += f"... and {len(structure) - limit} more files"
        
        return result
    
    def _format_commits(self, commits: List[Dict[str, Any]]) -> str:
        """Format commit information for display"""
        result = f"Recent Commits ({len(commits)}):\n"
        for commit in commits:
            result += f"• {commit.get('short_id', 'N/A')}: {commit.get('title', 'N/A')}\n"
            result += f"  {commit.get('author_name', 'N/A')} - {commit.get('created_at', 'N/A')}\n"
        
        return result
    
    def _get_repository_context_summary(self, max_tokens: int = 8000) -> str:
        """Get a summary of repository context for the AI"""
        self._initialize_context()
        
        context_parts = []
        
        if 'project_info' in self.repository_context:
            context_parts.append(self._format_project_info(self.repository_context['project_info']))
        
        if 'structure' in self.repository_context:
            context_parts.append(self._format_repository_structure(self.repository_context['structure']))
        
        if 'commits' in self.repository_context:
            context_parts.append(self._format_commits(self.repository_context['commits']))
        
        if 'readme' in self.repository_context and self.repository_context['readme']:
            readme_content = self.repository_context['readme']
            # Truncate README if too long
            if len(readme_content) > 2000:
                readme_content = readme_content[:2000] + "..."
            context_parts.append(f"README:\n{readme_content}")
        
        full_context = "\n\n".join(context_parts)
        
        # Truncate if too long
        if self.token_counter.count_tokens(full_context) > max_tokens:
            full_context = self.token_counter.truncate_text(full_context, max_tokens)
            full_context += "\n\n[Context truncated to fit token limit]"
        
        return full_context
    
    def answer_question(self, question: str) -> str:
        """Answer a question about the GitLab repository"""
        try:
            print(f"\n🤔 Processing question: {question}")
            
            # Get repository context (limited to prevent token overflow)
            repo_context = self._get_repository_context_summary(max_tokens=6000)
            
            # Check if the question is asking for specific file content
            additional_context = ""
            if "show me" in question.lower() and any(ext in question.lower() for ext in ['.py', '.js', '.md', '.txt', '.json', '.yml', '.yaml', '.cfg', '.ini']):
                words = question.split()
                for word in words:
                    if '.' in word and not word.startswith('.'):
                        try:
                            file_content = self.gitlab.get_file_content(word)
                            content = file_content.get('decoded_content', 'Could not decode content')
                            # Truncate file content if too long
                            if len(content) > 3000:
                                content = content[:3000] + "..."
                            additional_context += f"\n\nFile Content ({word}):\n{content}"
                            break
                        except Exception as e:
                            additional_context += f"\n\nCould not fetch {word}: {str(e)}"
                            continue
            
            # Check if question is about specific functionality
            elif any(keyword in question.lower() for keyword in ['function', 'class', 'method', 'code', 'implementation']):
                try:
                    relevant_files = self.gitlab.search_files(question)
                    if relevant_files:
                        additional_context += f"\n\nRelevant Files: {', '.join(relevant_files[:5])}"
                        # Get content of the first relevant file
                        if relevant_files:
                            try:
                                file_content = self.gitlab.get_file_content(relevant_files[0])
                                content = file_content.get('decoded_content', 'Could not decode content')
                                # Truncate file content
                                if len(content) > 2000:
                                    content = content[:2000] + "..."
                                additional_context += f"\n\nContent of {relevant_files[0]}:\n{content}"
                            except Exception as e:
                                additional_context += f"\n\nCould not fetch {relevant_files[0]}: {str(e)}"
                except Exception as e:
                    additional_context += f"\n\nError searching files: {str(e)}"
            
            # Combine contexts
            full_context = repo_context + additional_context
            
            # Final token check and truncation
            if self.token_counter.count_tokens(full_context) > 10000:
                full_context = self.token_counter.truncate_text(full_context, 10000)
                full_context += "\n\n[Context truncated to fit token limit]"
            
            # Prepare messages for Mistral
            system_message = {
                "role": "system",
                "content": f"""You are an expert assistant for GitLab repositories. Answer questions about:
- Project structure and files
- Code content and functionality  
- Recent commits and changes
- Project configuration

Repository Information:
{full_context}"""
            }
            
            user_message = {
                "role": "user",
                "content": question
            }
            
            messages = [system_message, user_message]
            
            # Add limited conversation history
            if self.conversation_history:
                # Only add last 2 exchanges to save tokens
                recent_history = self.conversation_history[-4:]
                messages.extend(recent_history)
            
            # Final token count
            total_tokens = self.token_counter.count_messages_tokens(messages)
            print(f"📊 Total context tokens: {total_tokens}")
            
            # Get response from Mistral
            response = self.mistral.chat(messages, max_tokens=1000)
            
            # Update conversation history (keep only last 6 messages)
            self.conversation_history.extend([
                {"role": "user", "content": question},
                {"role": "assistant", "content": response}
            ])
            
            # Keep conversation history manageable
            if len(self.conversation_history) > 12:
                self.conversation_history = self.conversation_history[-12:]
            
            return response
            
        except Exception as e:
            return f"Error processing question: {str(e)}"
    
    def get_file_content(self, file_path: str) -> str:
        """Get specific file content"""
        try:
            file_data = self.gitlab.get_file_content(file_path)
            content = file_data.get('decoded_content', '')
            
            result = f"File: {file_path}\n"
            result += f"Size: {file_data.get('size', 'N/A')} bytes\n"
            result += f"Content:\n{'-' * 50}\n{content}\n{'-' * 50}"
            
            return result
        except Exception as e:
            return f"Error fetching file content: {str(e)}"
    
    def get_project_info(self) -> str:
        """Get project information"""
        try:
            project_info = self.gitlab.get_project_info()
            return self._format_project_info(project_info)
        except Exception as e:
            return f"Error fetching project info: {str(e)}"
    
    def get_repository_structure(self) -> str:
        """Get repository structure"""
        try:
            structure = self.gitlab.get_repository_structure()
            return self._format_repository_structure(structure)
        except Exception as e:
            return f"Error fetching repository structure: {str(e)}"
    
    def get_recent_commits(self) -> str:
        """Get recent commits"""
        try:
            commits = self.gitlab.get_recent_commits()
            return self._format_commits(commits)
        except Exception as e:
            return f"Error fetching commits: {str(e)}"

# Example usage
def main():
    """Example usage of the GitLab Repository Agent"""
    
    print("GitLab Repository Q&A Agent with Token Counting")
    print("=" * 60)
    
    # Initialize the agent
    try:
        agent = GitLabRepositoryAgent()
        print("✅ Agent initialized successfully!")
    except Exception as e:
        print(f"❌ Error initializing agent: {e}")
        return
    
    # Example questions
    example_questions = [
        "What is this repository about?",
        "Show me the project structure", 
        "What are the recent commits?",
        "What programming languages are used?",
        "Show me the main configuration files",
        "What does the README file contain?",
        "Show me requirements.txt",
        "What functions are in the main file?",
    ]
    
    print("\nExample questions you can ask:")
    for i, question in enumerate(example_questions, 1):
        print(f"{i}. {question}")
    
    print("\n" + "=" * 60)
    
    # Interactive loop
    while True:
        try:
            question = input("\nYour question (or 'quit' to exit): ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if not question:
                continue
            
            print(f"\n{'='*60}")
            answer = agent.answer_question(question)
            print(f"\nAnswer:\n{answer}")
            print(f"{'='*60}")
            
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()t_tools() 
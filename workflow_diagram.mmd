---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	get_input(get_input)
	fetch_commits(fetch_commits)
	compare_llm(compare_llm)
	output_result(output_result)
	__end__([<p>__end__</p>]):::last
	__start__ --> get_input;
	compare_llm --> output_result;
	fetch_commits --> compare_llm;
	get_input --> fetch_commits;
	output_result --> __end__;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc

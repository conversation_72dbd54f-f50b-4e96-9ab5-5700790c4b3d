import requests
from datetime import datetime
from typing import Optional, Dict, Any
import base64


class GitLabAPIError(Exception):
    """Custom exception for GitLab API errors"""
    pass


def get_file_sha(commit_ref: str, file_path: str, 
                 gitlab_url: str, project_id: str, token: str) -> str:
    print('get_file_sha')
    """
    Get the SHA hash of a file at a specific commit reference.
    
    Args:
        commit_ref: Commit SHA, branch name, or tag
        file_path: Path to the file in the repository
        gitlab_url: GitLab instance URL (e.g., 'https://gitlab.com')
        project_id: GitLab project ID
        token: GitLab access token
        
    Returns:
        str: SHA hash of the file
        
    Raises:
        GitLabAPIError: If the API request fails or file is not found
    """
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # URL encode the file path
    encoded_file_path = requests.utils.quote(file_path, safe='')
    
    url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/files/{encoded_file_path}"
    
    params = {
        'ref': commit_ref
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        
        file_data = response.json()                  
        return file_data.get('blob_id')
        
    except requests.exceptions.HTTPError as e:
        if response.status_code == 404:
            raise GitLabAPIError(f"File '{file_path}' not found at commit '{commit_ref}'")
        else:
            raise GitLabAPIError(f"HTTP error {response.status_code}: {response.text}")
    except requests.exceptions.RequestException as e:
        raise GitLabAPIError(f"Request failed: {str(e)}")
    except KeyError:
        raise GitLabAPIError("Invalid response format from GitLab API")


def get_file_sha_from_branch_ts(branch: str, timestamp: str, file_path: str,
                                gitlab_url: str, project_id: str, token: str) -> str:
    print('get_file_sha_from_branch_ts')
    """
    Get the SHA hash of a file from the latest commit on a branch before a given timestamp.
    
    Args:
        branch: Branch name
        timestamp: ISO format timestamp (e.g., '2024-01-15T10:30:00Z')
        file_path: Path to the file in the repository
        gitlab_url: GitLab instance URL (e.g., 'https://gitlab.com')
        project_id: GitLab project ID
        token: GitLab access token
        
    Returns:
        str: SHA hash of the file
        
    Raises:
        GitLabAPIError: If the API request fails or no commits found before timestamp
    """
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # Get commits from the branch before the timestamp
    commits_url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/commits"
    
    params = {
        'ref_name': branch,
        'until': timestamp,
        'per_page': 1,  # We only need the latest commit
        'order': 'default'  # Most recent first
    }
    
    try:
        response = requests.get(commits_url, headers=headers, params=params)
        response.raise_for_status()
        
        commits = response.json()
        
        if not commits:
            raise GitLabAPIError(f"No commits found on branch '{branch}' before timestamp '{timestamp}'")
        
        # Get the latest commit SHA
        latest_commit_sha = commits[0]['id']
        
        # Now get the file SHA from this commit
        return get_file_sha(latest_commit_sha, file_path, gitlab_url, project_id, token)
        
    except requests.exceptions.HTTPError as e:
        raise GitLabAPIError(f"HTTP error {response.status_code}: {response.text}")
    except requests.exceptions.RequestException as e:
        raise GitLabAPIError(f"Request failed: {str(e)}")
    except (KeyError, IndexError) as e:
        raise GitLabAPIError(f"Invalid response format from GitLab API: {str(e)}")


def get_file_content(file_sha: str, gitlab_url: str, project_id: str, token: str) -> str:
    print('get_file_content')
    """
    Get the content of a file using its SHA hash.
    
    Args:
        file_sha: SHA hash of the file blob
        gitlab_url: GitLab instance URL (e.g., 'https://gitlab.com')
        project_id: GitLab project ID
        token: GitLab access token
        
    Returns:
        str: Content of the file
        
    Raises:
        GitLabAPIError: If the API request fails or file is not found
    """
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # Use the repository blobs API to get content by SHA
    url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/blobs/{file_sha}"
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        blob_data = response.json()
        
        # Content is base64 encoded, decode it
        content_b64 = blob_data.get('content', '')
        if not content_b64:
            raise GitLabAPIError("No content found in blob response")
            
        # Decode base64 content
        try:
            content = base64.b64decode(content_b64).decode('utf-8')
            return content
        except Exception as e:
            # If UTF-8 decoding fails, return as binary indicator
            raise GitLabAPIError(f"Cannot decode file content as UTF-8: {str(e)}")
            
    except requests.exceptions.HTTPError as e:
        if response.status_code == 404:
            raise GitLabAPIError(f"File with SHA '{file_sha}' not found")
        else:
            raise GitLabAPIError(f"HTTP error {response.status_code}: {response.text}")
    except requests.exceptions.RequestException as e:
        raise GitLabAPIError(f"Request failed: {str(e)}")
    except KeyError:
        raise GitLabAPIError("Invalid response format from GitLab API")


# Example usage and configuration
def create_gitlab_config(gitlab_url: str, project_id: str, token: str) -> Dict[str, str]:
    """
    Create a configuration dictionary for GitLab API calls.
    
    Args:
        gitlab_url: GitLab instance URL (e.g., 'https://gitlab.com')
        project_id: GitLab project ID
        token: GitLab access token
        
    Returns:
        Dict containing the configuration
    """
    return {
        'gitlab_url': gitlab_url,
        'project_id': project_id,
        'token': token
    }


# Wrapper functions for LangGraph tools (simplified interface)
def make_gitlab_tools(config: Dict[str, str]):
    """
    Create GitLab tool functions with pre-configured settings.
    
    Args:
        config: Configuration dictionary from create_gitlab_config()
        
    Returns:
        Tuple of three functions ready to use as LangGraph tools
    """
    
    def tool_get_file_sha(commit_ref: str, file_path: str) -> str:
        """Tool: Get file SHA from commit reference"""
        return get_file_sha(commit_ref, file_path, **config)
    
    def tool_get_file_sha_from_branch_ts(branch: str, timestamp: str, file_path: str) -> str:
        """Tool: Get file SHA from branch at timestamp"""
        return get_file_sha_from_branch_ts(branch, timestamp, file_path, **config)
    
    def tool_get_file_content(file_sha: str) -> str:
        """Tool: Get file content from SHA"""
        return get_file_content(file_sha, **config)
    
    return tool_get_file_sha, tool_get_file_sha_from_branch_ts, tool_get_file_content


'''# Example usage:
if __name__ == "__main__":
    # Configuration
    config = create_gitlab_config(
        gitlab_url="https://gitlab.com",
        project_id="71217006",
        token="**************************" )

    
    # Create tools
    get_sha_tool, get_sha_ts_tool, get_content_tool = make_gitlab_tools(config)
    
    # Example usage
    try:
        # Get file SHA from main branch
        sha = get_sha_tool("main", ".gitlab-ci.yml")
        print(f"File SHA: {sha}")
        
        # Get file SHA from branch at specific timestamp
        sha_ts = get_sha_ts_tool("main", "2025-07-7T10:30:00Z", ".gitlab-ci.yml")
        print(f"File SHA at timestamp: {sha_ts}")
        
        # Get file content
        content = get_content_tool(sha)
        print(f"File content: {content}")
        
    except GitLabAPIError as e:
        print(f"GitLab API Error: {e}")'''